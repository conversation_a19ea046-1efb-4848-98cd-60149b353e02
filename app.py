from flask import Flask, render_template, request, redirect, session, url_for, jsonify, send_file, send_from_directory, flash, make_response, Response
import mysql.connector
from functools import wraps
import time
import threading
import atexit
import io
from PIL import Image  # pip install Pillow
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
import json
import base64
import os
from werkzeug.utils import secure_filename
import datetime


# Store active users and their last activity time
active_users = {}
app = Flask(__name__)
app.secret_key = 'your_secret_key_here'
socketio = SocketIO(app, cors_allowed_origins="*")

app.config.update(
    SESSION_COOKIE_SECURE=True,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=1800
)

# Login required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('landing_page'))
        return f(*args, **kwargs)
    return decorated_function


# Database configuration
db_config = {
    'host': '**************',
    'user': 'giggenius_user',
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True
}

# Connection management
class ConnectionManager:
    def __init__(self):
        self.connection = None
        self.lock = threading.Lock()
        self.last_used = 0
        self.active_connections = 0
        self.max_connections = 5  # Limit total connections
        self.connection_timeout = 600  # 10 minutes

    def get_connection(self):
        """Get a database connection with proper tracking"""
        with self.lock:
            current_time = time.time()

            # Check if we need to create a new connection
            if (self.connection is None or
                not self._is_connection_alive() or
                current_time - self.last_used > self.connection_timeout):

                # Close existing connection if it exists
                if self.connection is not None:
                    try:
                        self.connection.close()
                        print("Closed stale connection")
                    except:
                        pass

                # Create new connection
                if self.active_connections < self.max_connections:
                    self.connection = self._create_connection()
                    self.active_connections += 1
                    print(f"Created new connection. Active: {self.active_connections}")
                else:
                    # Wait for a connection to become available
                    print("Waiting for connection to become available")
                    return None

            # Update last used time
            self.last_used = current_time
            return self.connection

    def _create_connection(self):
        """Create a new database connection"""
        try:
            connection = mysql.connector.connect(**db_config)
            # Test the connection
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            return connection
        except Exception as e:
            print(f"Error creating connection: {e}")
            return None

    def _is_connection_alive(self):
        """Check if the connection is still alive"""
        if self.connection is None:
            return False

        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            return True
        except:
            return False

    def release_connection(self):
        """Release a connection back to the pool"""
        with self.lock:
            self.active_connections -= 1
            print(f"Released connection. Active: {self.active_connections}")

    def close_all(self):
        """Close all connections"""
        with self.lock:
            if self.connection is not None:
                try:
                    self.connection.close()
                    print("Closed connection during shutdown")
                except:
                    pass
            self.connection = None
            self.active_connections = 0

# Create connection manager
conn_manager = ConnectionManager()

# Register shutdown function to close connections
@atexit.register
def shutdown():
    conn_manager.close_all()

# Function to get a connection with retry
def get_db_connection():
    """Get a database connection with retry logic"""
    max_retries = 3
    retry_delay = 1

    for attempt in range(max_retries):
        connection = conn_manager.get_connection()
        if connection is not None:
            return connection

        if attempt < max_retries - 1:
            time.sleep(retry_delay)
            retry_delay *= 2

    # Last resort - create a direct connection
    print("Creating direct connection as last resort")
    try:
        return mysql.connector.connect(**db_config)
    except Exception as e:
        print(f"Failed to create direct connection: {e}")
        raise

# Cache decorator for expensive database operations
def cache_result(timeout=300):  # 5 minutes default cache
    def decorator(f):
        cache = {}
        @wraps(f)
        def decorated_function(*args, **kwargs):
            key = str(args) + str(kwargs)
            if key in cache:
                result, timestamp = cache[key]
                if time.time() - timestamp < timeout:
                    return result
            result = f(*args, **kwargs)
            cache[key] = (result, time.time())
            return result
        return decorated_function
    return decorator


@app.route('/')
def landing_page():
    # If user is already logged in, redirect to appropriate page
    if 'user_id' in session:
        if session.get('user_type') == 'genius':
            return redirect(url_for('genius_page'))
        elif session.get('user_type') == 'client':
            return redirect(url_for('client_page'))
    return render_template('landing_page.html')

@app.route('/login', methods=['POST'])
def login():
    email = request.form['email']
    password = request.form['password']
    connection_created = False

    try:
        # Get a connection
        conn = get_db_connection()
        if conn is None:
            return jsonify(success=False, error="Server is busy. Please try again shortly.")

        cursor = conn.cursor(dictionary=True)

        # First try genius login
        query = """
            SELECT id, email, first_name, last_name,
                   IF(profile_photo IS NULL, 0, 1) as has_photo
            FROM approve_genius
            WHERE email = %s AND password = %s
        """
        cursor.execute(query, (email, password))
        user = cursor.fetchone()

        if user:
            session.clear()
            session['user_id'] = user['id']
            session['email'] = user['email']
            session['name'] = f"{user['first_name']} {user['last_name']}"
            session['has_profile_photo'] = bool(user['has_photo'])
            session['user_type'] = 'genius'
            session.permanent = True

            # Return success with replace_history flag
            return jsonify(success=True, redirect=url_for('genius_page'), replace_history=True)

        # If not found, try client login
        query = """
            SELECT id, work_email, first_name, last_name,
                   IF(profile_photo IS NULL, 0, 1) as has_photo,
                   business_name, position
            FROM approve_client
            WHERE work_email = %s AND password = %s
        """
        cursor.execute(query, (email, password))
        user = cursor.fetchone()

        if user:
            session.clear()
            session['user_id'] = user['id']
            session['email'] = user['work_email']
            session['name'] = f"{user['first_name']} {user['last_name']}"
            session['has_profile_photo'] = bool(user['has_photo'])
            session['business_name'] = user['business_name']
            session['position'] = user['position']
            session['user_type'] = 'client'
            session.permanent = True

            # Return success with replace_history flag
            return jsonify(success=True, redirect=url_for('client_page'), replace_history=True)

        cursor.close()

        # If we get here, no user was found
        return jsonify(success=False, error="Invalid email or password")

    except mysql.connector.errors.OperationalError as e:
        if "Too many connections" in str(e):
            print("Too many connections error")
            return jsonify(success=False, error="Database is busy. Please try again in a moment.")
        else:
            print(f"Operational error: {e}")
            return jsonify(success=False, error="Database connection error. Please try again.")
    except Exception as e:
        print(f"Login error: {e}")
        return jsonify(success=False, error="Something went wrong. Please try again.")
    finally:
        # Release the connection back to the pool
        if not connection_created:
            conn_manager.release_connection()

@app.route('/genius_profile')
def genius_profile():
    if 'user_id' in session and session.get('user_type') == 'genius':
        # Fetch genius data from the database
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)

            # Get all profile data including all fields needed for editing
            profile_query = """
                SELECT id, profile_photo, first_name, last_name, email, mobile,
                       position, expertise, hourly_rate, availability, country,
                       introduction, professional_sum
                FROM approve_genius
                WHERE id = %s
            """
            cursor.execute(profile_query, (session.get('user_id'),))
            profile_data = cursor.fetchone()

            cursor.close()
            conn.close()

            # Create genius object with profile data
            genius = {
                'id': profile_data.get('id', ''),
                'position': profile_data.get('position', 'Not specified'),
                'country': profile_data.get('country', 'Not specified'),
                'hourly_rate': profile_data.get('hourly_rate', 0),
                'first_name': profile_data.get('first_name', ''),
                'last_name': profile_data.get('last_name', ''),
                'email': profile_data.get('email', ''),
                'mobile': profile_data.get('mobile', ''),
                'expertise': profile_data.get('expertise', ''),
                'availability': profile_data.get('availability', ''),
                'introduction': profile_data.get('introduction', 'No introduction provided.'),
                'professional_sum': profile_data.get('professional_sum', '')
            }

            # Handle profile photo
            if profile_data and profile_data.get('profile_photo'):
                genius['profile_picture_url'] = f"data:image/jpeg;base64,{base64.b64encode(profile_data['profile_photo']).decode()}"
            else:
                genius['profile_picture_url'] = url_for('static', filename='img/default_profile.png')

            return render_template('genius_profile.html', genius=genius)

        except Exception as e:
            print(f"Error fetching genius profile data: {e}")
            # Provide default genius data in case of error
            genius = {
                'id': '',
                'position': 'Not specified',
                'country': 'Not specified',
                'hourly_rate': 0,
                'first_name': session.get('name', '').split()[0] if session.get('name') else '',
                'last_name': session.get('name', '').split()[-1] if session.get('name') else '',
                'email': session.get('email', ''),
                'mobile': '',
                'expertise': '',
                'availability': '',
                'introduction': 'No introduction provided.',
                'professional_sum': '',
                'profile_picture_url': url_for('static', filename='img/default_profile.png')
            }
            return render_template('genius_profile.html', genius=genius)
    return redirect(url_for('landing_page'))

@app.route('/update_professional_summary', methods=['POST'])
def update_professional_summary():
    if 'user_id' not in session or session.get('user_type') != 'genius':
        return jsonify({'success': False, 'error': 'Unauthorized'}), 401

    try:
        data = request.get_json()
        professional_summary = data.get('professional_summary', '').strip()

        if not professional_summary:
            return jsonify({'success': False, 'error': 'Professional summary cannot be empty'}), 400

        # Update the database
        conn = get_db_connection()
        cursor = conn.cursor()

        update_query = """
            UPDATE approve_genius
            SET professional_sum = %s
            WHERE id = %s
        """
        cursor.execute(update_query, (professional_summary, session.get('user_id')))
        conn.commit()

        cursor.close()
        conn.close()

        return jsonify({'success': True, 'message': 'Professional summary updated successfully'})

    except Exception as e:
        print(f"Error updating professional summary: {e}")
        return jsonify({'success': False, 'error': 'Failed to update professional summary'}), 500

@app.route('/introduction', methods=['POST'])
def introduction():
    if 'user_id' not in session or session.get('user_type') != 'genius':
        return jsonify({'success': False, 'error': 'Unauthorized'}), 401

    try:
        data = request.get_json()
        introduction = data.get('introduction', '').strip()

        if not introduction:
            return jsonify({'success': False, 'error': 'Introduction cannot be empty'}), 400

        # Update the database
        conn = get_db_connection()
        cursor = conn.cursor()

        update_query = """
            UPDATE approve_genius
            SET introduction = %s
            WHERE id = %s
        """
        cursor.execute(update_query, (introduction, session.get('user_id')))
        conn.commit()

        cursor.close()
        conn.close()

        return jsonify({'success': True, 'message': 'Introduction updated successfully'})

    except Exception as e:
        print(f"Error updating introduction: {e}")
        return jsonify({'success': False, 'error': 'Failed to update introduction'}), 500

@app.route('/update_genius_profile', methods=['POST'])
def update_genius_profile():
    if 'user_id' not in session or session.get('user_type') != 'genius':
        return jsonify({'success': False, 'error': 'Unauthorized'}), 401

    try:
        # Check if this is a form data request (with file upload) or JSON request
        if request.content_type and 'multipart/form-data' in request.content_type:
            # Handle form data with file upload
            email = request.form.get('email', '').strip()
            mobile = request.form.get('mobile', '').strip()
            position = request.form.get('position', '').strip()
            expertise = request.form.get('expertise', '').strip()
            hourly_rate = request.form.get('hourly_rate')
            availability = request.form.get('availability', '').strip()
            country = request.form.get('country', '').strip()
            language = request.form.get('language', '').strip()

            # Handle profile photo upload
            profile_photo = None
            if 'profile_photo' in request.files:
                file = request.files['profile_photo']
                if file.filename != '':
                    profile_photo = file.read()
        else:
            # Handle JSON data (no file upload)
            data = request.get_json()
            email = data.get('email', '').strip()
            mobile = data.get('mobile', '').strip()
            position = data.get('position', '').strip()
            expertise = data.get('expertise', '').strip()
            hourly_rate = data.get('hourly_rate')
            availability = data.get('availability', '').strip()
            country = data.get('country', '').strip()
            language = data.get('language', '').strip()
            profile_photo = None

        # Validate required fields
        if not email or not mobile or not position or not expertise or not availability or not country:
            return jsonify({'success': False, 'error': 'Please fill in all required fields'}), 400

        # Validate email format
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return jsonify({'success': False, 'error': 'Please enter a valid email address'}), 400

        # Validate hourly rate
        try:
            hourly_rate = float(hourly_rate)
            if hourly_rate < 0:
                return jsonify({'success': False, 'error': 'Hourly rate must be a positive number'}), 400
        except (ValueError, TypeError):
            return jsonify({'success': False, 'error': 'Please enter a valid hourly rate'}), 400

        # Update the database
        conn = get_db_connection()
        cursor = conn.cursor()

        if profile_photo:
            # Update with profile photo
            update_query = """
                UPDATE approve_genius
                SET email = %s, mobile = %s, position = %s, expertise = %s,
                    hourly_rate = %s, availability = %s, country = %s, profile_photo = %s
                WHERE id = %s
            """
            cursor.execute(update_query, (
                email, mobile, position, expertise, hourly_rate,
                availability, country, profile_photo, session.get('user_id')
            ))
        else:
            # Update without profile photo
            update_query = """
                UPDATE approve_genius
                SET email = %s, mobile = %s, position = %s, expertise = %s,
                    hourly_rate = %s, availability = %s, country = %s
                WHERE id = %s
            """
            cursor.execute(update_query, (
                email, mobile, position, expertise, hourly_rate,
                availability, country, session.get('user_id')
            ))

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({'success': True, 'message': 'Profile updated successfully'})

    except Exception as e:
        print(f"Error updating genius profile: {e}")
        return jsonify({'success': False, 'error': 'Failed to update profile'}), 500

@app.route('/test_db')
def test_db():
    """Test route to check database values"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get all genius profiles to see what data exists
        cursor.execute("SELECT id, first_name, last_name, email, position, country, availability, expertise FROM approve_genius LIMIT 5")
        profiles = cursor.fetchall()

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'profiles': profiles
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# GENIUS REGISTRATION ROUTE
@app.route('/register_genius', methods=['GET', 'POST'])
def register_genius():
    if request.method == 'POST':
        try:
            conn = get_db_connection()
            if conn is None:
                return jsonify({
                    'success': False,
                    'error': 'Database connection failed'
                })

            cursor = conn.cursor()

            # Handle file uploads with better error checking and debugging
            profile_photo = None
            id_front = None
            id_back = None

            # Debug print
            print("Files received:", list(request.files.keys()))

            if 'profilePhoto' in request.files:
                file = request.files['profilePhoto']
                if file.filename != '':
                    profile_photo = file.read()
                    print("Profile photo size:", len(profile_photo))

            if 'idFront' in request.files:
                file = request.files['idFront']
                if file.filename != '':
                    id_front = file.read()
                    print("ID Front size:", len(id_front))

            if 'idBack' in request.files:
                file = request.files['idBack']
                if file.filename != '':
                    id_back = file.read()
                    print("ID Back size:", len(id_back))

            # Validate that ID photos are present
            if not id_front or not id_back:
                return jsonify({
                    'success': False,
                    'error': 'Both front and back ID photos are required'
                }), 400

            # Validate required fields
            required_fields = ['firstName', 'lastName', 'email', 'password', 'birthday',
                             'country', 'mobile', 'position', 'expertise', 'hourly_rate',
                             'availability', 'tax_id', 'introduction', 'professional_sum']

            missing_fields = [field for field in required_fields
                            if field not in request.form or not request.form[field].strip()]

            if missing_fields:
                return jsonify({
                    'success': False,
                    'error': f'Missing required fields: {", ".join(missing_fields)}'
                }), 400

            # Convert checkbox values to tinyint with default 0
            email_updates = 1 if request.form.get('email_updates') == '1' else 0
            terms_agreement = 1 if request.form.get('terms_agreement') == '1' else 0

            # SQL query
            sql = """
            INSERT INTO register_genius (
                profile_photo, first_name, last_name, email, password,
                birthday, country, mobile, position, expertise,
                hourly_rate, availability, tax_id, introduction, professional_sum,
                id_front, id_back, email_updates, terms_agreement
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            # Prepare data tuple
            data = (
                profile_photo,
                request.form['firstName'].strip(),
                request.form['lastName'].strip(),
                request.form['email'].strip(),
                request.form['password'],
                request.form['birthday'],
                request.form['country'],
                request.form['mobile'].strip(),
                request.form['position'],
                request.form['expertise'],
                float(request.form['hourly_rate']),
                request.form['availability'],
                request.form['tax_id'].strip(),
                request.form['introduction'].strip(),
                request.form['professional_sum'].strip(),
                id_front,
                id_back,
                email_updates,
                terms_agreement
            )

            # Debug print the data tuple (excluding binary data)
            print("Data to be inserted:", [
                "binary_data" if isinstance(x, bytes) else x
                for x in data
            ])

            cursor.execute(sql, data)
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'Registration successful'
            })

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Database error: {str(err)}'
            }), 500

        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Unexpected error: {str(e)}'
            }), 500

        finally:
            if 'cursor' in locals():
                cursor.close()
            if conn:
                conn.close()

    return render_template('genius_registration.html')

@app.route('/genius_registration', methods=['GET', 'POST'])
def genius_registration():
    return render_template('genius_registration.html')

@app.route('/client_registration', methods=['GET', 'POST'])
def client_registration():
    return render_template('client_registration.html')

# CLIENT REGISTRATION ROUTE
@app.route('/register_client', methods=['GET', 'POST'])
def register_client():
    if request.method == 'POST':
        try:
            conn = get_db_connection()
            if conn is None:
                return jsonify({
                    'success': False,
                    'error': 'Database connection failed'
                })

            cursor = conn.cursor()

            # Handle file uploads
            profile_photo = None
            business_logo = None
            business_reg_doc = None

            print("Files received:", list(request.files.keys()))

            if 'profilePhoto' in request.files:
                file = request.files['profilePhoto']
                if file.filename != '':
                    profile_photo = file.read()
                    print("Profile photo size:", len(profile_photo))

            if 'businessLogo' in request.files:
                file = request.files['businessLogo']
                if file.filename != '':
                    business_logo = file.read()
                    print("Business logo size:", len(business_logo))

            if 'businessReg' in request.files:
                file = request.files['businessReg']
                if file.filename != '':
                    business_reg_doc = file.read()
                    print("Business registration doc size:", len(business_reg_doc))

            # Validate required fields
            required_fields = [
                'firstName', 'lastName', 'email', 'password', 'birthday',
                'country', 'mobile', 'position', 'businessName',
                'businessAddress', 'businessEmail', 'industry',
                'employeeCount'
            ]

            missing_fields = [field for field in required_fields if not request.form.get(field)]
            if missing_fields:
                return jsonify({
                    'success': False,
                    'error': f'Missing required fields: {", ".join(missing_fields)}'
                }), 400

            # Convert checkbox values to tinyint
            email_updates = 1 if request.form.get('email_updates') == '1' else 0
            terms_agreement = 1 if request.form.get('terms_agreement') == '1' else 0

            # SQL query
            sql = """
            INSERT INTO register_client (
                profile_photo, first_name, last_name, work_email, password,
                birthday, country, mobile, position, business_logo,
                business_name, business_address, business_email, industry,
                business_website, employee_count, introduction, business_registration_doc,
                email_updates, terms_agreement, status
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            # Prepare data tuple
            data = (
                profile_photo,
                request.form['firstName'].strip(),
                request.form['lastName'].strip(),
                request.form['email'].strip(),
                request.form['password'],
                request.form['birthday'],
                request.form['country'],
                request.form['mobile'].strip(),
                request.form['position'].strip(),
                business_logo,
                request.form['businessName'].strip(),
                request.form['businessAddress'].strip(),
                request.form['businessEmail'].strip(),
                request.form['industry'],
                request.form.get('businessWebsite', '').strip(),
                request.form['employeeCount'],
                request.form['introduction'].strip(),
                business_reg_doc,
                email_updates,
                terms_agreement,
                'pending'
            )

            # Debug print the data tuple (excluding binary data)
            print("Data to be inserted:", [
                "binary_data" if isinstance(x, bytes) else x
                for x in data
            ])

            cursor.execute(sql, data)
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'Registration successful'
            })

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Database error: {str(err)}'
            }), 500

        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Unexpected error: {str(e)}'
            }), 500

        finally:
            if 'cursor' in locals():
                cursor.close()
            if conn:
                conn.close()

    return render_template('client_registration.html')


@app.route('/get_genius_details/<int:id>')
def get_genius_details(id):
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)

    cursor.execute("SELECT * FROM register_genius WHERE id = %s", (id,))
    genius = cursor.fetchone()

    cursor.close()
    conn.close()

    if genius:
        # Convert binary data to base64 for images
        if genius['profile_photo']:
            genius['profile_photo'] = base64.b64encode(genius['profile_photo']).decode()
        if genius['id_front']:
            genius['id_front'] = base64.b64encode(genius['id_front']).decode()
        if genius['id_back']:
            genius['id_back'] = base64.b64encode(genius['id_back']).decode()

        return jsonify(genius)

    return jsonify({'error': 'Genius not found'}), 404

@app.route('/update_genius_status', methods=['POST'])
def update_genius_status():
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    data = request.json
    genius_id = data.get('genius_id')
    status = data.get('status')

    if not genius_id or not status:
        return jsonify({'error': 'Missing genius_id or status'}), 400

    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)

    try:
        if status == 'approved':
            # Get genius data
            cursor.execute("SELECT * FROM register_genius WHERE id = %s", (genius_id,))
            genius = cursor.fetchone()

            if not genius:
                return jsonify({'error': 'Genius not found'}), 404

            # Insert into approve_genius
            cursor.execute("""
                INSERT INTO approve_genius (
                    profile_photo, first_name, last_name, email, password,
                    birthday, country, mobile, position, expertise,
                    hourly_rate, availability, tax_id, introduction, professional_sum,
                    id_front, id_back, email_updates, terms_agreement
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """, (
                genius['profile_photo'], genius['first_name'], genius['last_name'],
                genius['email'], genius['password'], genius['birthday'],
                genius['country'], genius['mobile'], genius['position'],
                genius['expertise'], genius['hourly_rate'], genius['availability'],
                genius['tax_id'], genius['introduction'], genius['professional_sum'],
                genius['id_front'], genius['id_back'], genius['email_updates'],
                genius['terms_agreement']
            ))

            # Delete from register_genius
            cursor.execute("DELETE FROM register_genius WHERE id = %s", (genius_id,))

        else:
            # Update status for declined
            cursor.execute("UPDATE register_genius SET status = %s WHERE id = %s",
                         (status, genius_id))

        conn.commit()
        return jsonify({'success': True})

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        conn.rollback()
        return jsonify({'error': str(err)}), 500

    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
        return jsonify({'error': str(e)}), 500

    finally:
        cursor.close()
        conn.close()

@app.route('/update_client_status', methods=['POST'])
def update_client_status():
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    data = request.json
    client_id = data.get('client_id')
    status = data.get('status')

    if not client_id or not status:
        return jsonify({'error': 'Missing client_id or status'}), 400

    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)

    try:
        if status == 'approved':
            # Get client data from register_client
            cursor.execute("""
                SELECT
                    profile_photo,
                    first_name,
                    last_name,
                    work_email,
                    password,
                    birthday,
                    country,
                    mobile,
                    position,
                    business_logo,
                    business_name,
                    business_address,
                    business_email,
                    industry,
                    business_website,
                    employee_count,
                    introduction,  # Add this line
                    business_registration_doc,
                    email_updates,
                    terms_agreement,
                    created_at
                FROM register_client
                WHERE id = %s
            """, (client_id,))

            client = cursor.fetchone()

            if not client:
                return jsonify({'error': 'Client not found'}), 404

            # Insert into approve_client table
            cursor.execute("""
                INSERT INTO approve_client (
                    profile_photo,
                    first_name,
                    last_name,
                    work_email,
                    password,
                    birthday,
                    country,
                    mobile,
                    position,
                    business_logo,
                    business_name,
                    business_address,
                    business_email,
                    industry,
                    business_website,
                    employee_count,
                    introduction,
                    business_registration_doc,
                    email_updates,
                    terms_agreement,
                    created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """, (
                client['profile_photo'],
                client['first_name'],
                client['last_name'],
                client['work_email'],
                client['password'],
                client['birthday'],
                client['country'],
                client['mobile'],
                client['position'],
                client['business_logo'],
                client['business_name'],
                client['business_address'],
                client['business_email'],
                client['industry'],
                client['business_website'],
                client['employee_count'],
                client['introduction'],
                client['business_registration_doc'],
                client['email_updates'],
                client['terms_agreement'],
                client['created_at']
            ))

            # Delete from register_client after successful insertion
            cursor.execute("DELETE FROM register_client WHERE id = %s", (client_id,))

        else:
            # Update status for declined
            cursor.execute("UPDATE register_client SET status = %s WHERE id = %s",
                         (status, client_id))

        conn.commit()
        return jsonify({'success': True})

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        conn.rollback()
        return jsonify({'error': str(err)}), 500

    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
        return jsonify({'error': str(e)}), 500

    finally:
        cursor.close()
        conn.close()

@app.route('/get_client_details/<int:client_id>')
def get_client_details(client_id):
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)

        cursor.execute("""
            SELECT
                id,
                first_name,
                last_name,
                work_email,
                DATE_FORMAT(birthday, '%Y-%m-%d') as birthday,
                country,
                mobile,
                position,
                business_name,
                business_address,
                business_email,
                industry,
                business_website as website,
                employee_count,
                introduction,
                status,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') as created_at
            FROM register_client
            WHERE id = %s
        """, (client_id,))

        client = cursor.fetchone()

        if client:
            # Handle binary data separately
            cursor.execute("SELECT profile_photo, business_logo, business_registration_doc FROM register_client WHERE id = %s", (client_id,))
            binary_data = cursor.fetchone()

            # Convert binary data to base64 if exists
            if binary_data['profile_photo']:
                client['profile_photo'] = f"data:image/jpeg;base64,{base64.b64encode(binary_data['profile_photo']).decode()}"

            if binary_data['business_logo']:
                client['business_logo'] = f"data:image/jpeg;base64,{base64.b64encode(binary_data['business_logo']).decode()}"

            if binary_data['business_registration_doc']:
                # Specifically format PDF data
                client['business_registration_doc'] = f"data:application/pdf;base64,{base64.b64encode(binary_data['business_registration_doc']).decode()}"

            return jsonify(client)

        return jsonify({'error': 'Client not found'}), 404

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        return jsonify({'error': 'Database error occurred'}), 500

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()


@app.route('/genius_page')
@app.route('/genius_page/<int:page>')
@login_required
def genius_page(page=1):
    if session.get('user_type') == 'genius':
        # Fetch additional profile information
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)

            # Get profile data including introduction
            profile_query = """
                SELECT position, country, hourly_rate, profile_photo, first_name, last_name, introduction
                FROM approve_genius
                WHERE id = %s
            """
            cursor.execute(profile_query, (session.get('user_id'),))
            profile_data = cursor.fetchone()

            # Create genius object
            genius = {
                'profile_picture_url': url_for('get_profile_photo', user_id=session.get('user_id')),
                'position': profile_data.get('position', 'Not specified') if profile_data else 'Not specified',
                'country': profile_data.get('country', 'Not specified') if profile_data else 'Not specified',
                'hourly_rate': profile_data.get('hourly_rate', '0') if profile_data else '0',
                'first_name': profile_data.get('first_name', '') if profile_data else session.get('first_name', ''),
                'last_name': profile_data.get('last_name', '') if profile_data else session.get('last_name', ''),
                'introduction': profile_data.get('introduction', '') if profile_data else ''
            }

            # Pagination settings - changed from 6 to 5 jobs per page
            per_page = 5
            offset = (page - 1) * per_page

            # Check if status column exists
            cursor.execute("""
                SELECT COUNT(*) as column_exists
                FROM information_schema.columns
                WHERE table_schema = DATABASE()
                AND table_name = 'job_submissions'
                AND column_name = 'status'
            """)
            status_exists = cursor.fetchone()['column_exists'] > 0

            # Get total job count for pagination
            if status_exists:
                count_query = """
                    SELECT COUNT(*) as total
                    FROM job_submissions
                    WHERE status = 'active'
                """
            else:
                count_query = """
                    SELECT COUNT(*) as total
                    FROM job_submissions
                """
            cursor.execute(count_query)
            total_jobs = cursor.fetchone()['total']

            # Calculate total pages
            total_pages = (total_jobs + per_page - 1) // per_page

            # Get paginated job listings
            if status_exists:
                jobs_query = """
                    SELECT id, title, description, project_size, duration,
                           budget_type, budget_amount, category, specialty,
                           created_at, job_type
                    FROM job_submissions
                    WHERE status = 'active'
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                """
            else:
                jobs_query = """
                    SELECT id, title, description, project_size, duration,
                           budget_type, budget_amount, category, specialty,
                           created_at, job_type
                    FROM job_submissions
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                """
            cursor.execute(jobs_query, (per_page, offset))
            jobs = cursor.fetchall()

            cursor.close()
            conn.close()

            return render_template(
                'genius_page.html',
                genius=genius,  # Pass the genius object
                jobs=jobs,
                current_page=page,
                total_pages=total_pages
            )
        except Exception as e:
            print(f"Error fetching data: {e}")
            # Create default genius object if there's an error
            genius = {
                'profile_picture_url': url_for('static', filename='img/default_profile.png'),
                'position': 'Not specified',
                'country': 'Not specified',
                'hourly_rate': '0',
                'first_name': session.get('first_name', ''),
                'last_name': session.get('last_name', ''),
                'introduction': ''
            }
            jobs = []
            total_pages = 1

            return render_template(
                'genius_page.html',
                genius=genius,
                jobs=jobs,
                current_page=page,
                total_pages=total_pages
            )
    return redirect(url_for('landing_page'))

@app.route('/client_page')
@login_required
def client_page():
    if session.get('user_type') == 'client':
        # Fetch additional client information
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)

            # Get client data
            client_query = """
                SELECT country, industry, employee_count, profile_photo
                FROM approve_client
                WHERE id = %s
            """
            cursor.execute(client_query, (session.get('user_id'),))
            client_data = cursor.fetchone()

            # Create client object for template
            client = {
                'profile_picture_url': url_for('get_profile_photo', user_id=session.get('user_id')),
                'country': client_data.get('country', 'Not specified') if client_data else 'Not specified',
                'industry': client_data.get('industry', 'Not specified') if client_data else 'Not specified',
                'employee_count': client_data.get('employee_count', 'Not specified') if client_data else 'Not specified'
            }

            cursor.close()
            conn.close()

        except Exception as e:
            print(f"Error fetching client data: {e}")
            client = {
                'profile_picture_url': url_for('static', filename='img/default_profile.png'),
                'country': 'Not specified',
                'industry': 'Not specified',
                'employee_count': 'Not specified'
            }

        # Add empty jobs list and pagination variables for template
        jobs = []
        current_page = 1
        total_pages = 1

        return render_template(
            'client_page.html',
            name=session.get('name'),
            business_name=session.get('business_name'),
            position=session.get('position'),
            client=client,  # Pass the client object with country data
            jobs=jobs,
            current_page=current_page,
            total_pages=total_pages
        )
    return redirect(url_for('landing_page'))

@app.route('/client_profile')
def client_profile():
    if 'user_id' in session and session.get('user_type') == 'client':
        return render_template('client_profile.html')
    return redirect(url_for('landing_page'))

# FOOTER LINKS ROUTE
@app.route('/why_giggenius')
def why_giggenius():


    return render_template('why_giggenius.html')

@app.route('/affiliate_program')
def affiliate_program():
    return render_template('f_affiliate_program.html')

@app.route('/news_and_events')
def news_and_events():
    return render_template('f_news_and_events.html')

@app.route('/find_geniuses')
def find_geniuses():


    return render_template('find_geniuses.html')

@app.route('/find_gigs')
def find_gigs():


    return render_template('find_gigs.html')

@app.route('/how_to_hire')
def how_to_hire():


    return render_template('f_how_to_hire.html')

@app.route('/accounting_services')
def accounting_services():


    return render_template('f_accounting_services.html')

@app.route('/payroll_services')
def payroll_services():
    return render_template('f_payroll_services.html')

@app.route('/events')
def events():

    return render_template('f_events.html')

@app.route('/ph_business_loan')
def ph_business_loan():


    return render_template('f_ph_business_loan.html')

@app.route('/how_it_works')
def how_it_works():


    return render_template('f_how_it_works.html')

@app.route('/why_cant_apply')
def why_cant_apply():

    return render_template('f_why_cant_apply.html')

@app.route('/direct_contracts')
def direct_contracts():


    return render_template('f_direct_contracts.html')

@app.route('/find_mentors')
def find_mentors():


    return render_template('f_find_mentors.html')

@app.route('/mentor_application')
def mentor_application():


    return render_template('f_mentor_application.html')

@app.route('/ph_health_insurance')
def ph_health_insurance():
    # Check if user is logged in and redirect if needed

    return render_template('f_ph_health_insurance.html')

@app.route('/ph_life_insurance')
def ph_life_insurance():


    return render_template('f_ph_life_insurance.html')

@app.route('/help_and_support')
def help_and_support():
    return render_template('f_help_and_support.html')

@app.route('/business_networking')
def business_networking():
    return render_template('f_business_networking.html')

# Profile photo retrieval route
@app.route('/get_profile_photo/<int:user_id>')
def get_profile_photo(user_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check user type from session
        user_type = session.get('user_type')

        if user_type == 'genius':
            query = "SELECT profile_photo FROM approve_genius WHERE id = %s"
        elif user_type == 'client':
            query = "SELECT profile_photo FROM approve_client WHERE id = %s"
        else:
            return redirect(url_for('static', filename='img/default_profile.png'))

        cursor.execute(query, (user_id,))
        result = cursor.fetchone()

        cursor.close()
        conn.close()

        if result and result['profile_photo']:
            response = make_response(result['profile_photo'])
            response.headers.set('Content-Type', 'image/jpeg')
            return response
        else:
            return redirect(url_for('static', filename='img/default_profile.png'))
    except Exception as e:
        print(f"Error fetching profile photo: {e}")
        return redirect(url_for('static', filename='img/default_profile.png'))

@app.route('/about_us')
def about_us():
    return render_template('f_about_us.html')

@app.route('/contact_us')
def contact_us():
    return render_template('f_contact_us.html')

@app.route('/charity_projects')
def charity_projects():
    return render_template('f_charity_projects.html')

@app.route('/terms_of_service')
def terms_of_service():
    return render_template('f_terms_of_service.html')

@app.route('/privacy_policy')
def privacy_policy():
    return render_template('f_privacy_policy.html')

@app.route('/marketplace')
def marketplace():
    return render_template('marketplace.html')

@app.route('/service_catalog')
def service_catalog():
    return render_template('f_service_catalog.html')





@app.route('/affiliate_dashboard')
def affiliate_dashboard():
    # Check if user is logged in as affiliate
    if 'affiliate_id' not in session:
        return redirect(url_for('landing_page'))

    # Get affiliate data from database
    affiliate_id = session.get('affiliate_id')

    conn = get_db_connection()
    if conn is None:
        return "Database connection failed", 500

    cursor = conn.cursor(dictionary=True)

    # Get affiliate data with all needed fields
    cursor.execute("""
        SELECT
            id, first_name, last_name, email, phone, referral_code,
            status, commission_rate_freelancer, commission_rate_client,
            total_earnings, profile_photo
        FROM affiliates
        WHERE id = %s
    """, (affiliate_id,))

    affiliate = cursor.fetchone()

    if not affiliate:
        session.clear()
        return redirect(url_for('landing_page'))

    # Add name field for template
    affiliate['name'] = f"{affiliate['first_name']} {affiliate['last_name']}"

    # Set default profile photo if none exists
    if not affiliate.get('profile_photo'):
        affiliate['profile_photo'] = url_for('static', filename='img/default-profile.jpg')

    # Get referrals data (placeholder - implement actual query)
    referrals = []  # You'll need to implement this query

    # Add current date/time for the template
    from datetime import datetime
    now = datetime.now()

    # Add total_referrals field
    affiliate['total_referrals'] = len(referrals)

    cursor.close()
    conn.close()

    # Pass all required variables to the template
    return render_template('affiliate_dashboard.html',
                          affiliate=affiliate,
                          referrals=referrals,
                          now=now,
                          request=request)


@app.route('/job_details/<int:job_id>')
def job_details(job_id):
    """Display detailed information about a specific job submission"""
    if 'user_id' not in session:
        return redirect(url_for('landing_page'))

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get job details from job_submissions table with more client information
        job_query = """
            SELECT
                j.id, j.client_id, j.title, j.description, j.project_size,
                j.project_description, j.duration, j.experience_level,
                j.hiring_preference, j.budget_type, j.budget_amount,
                j.category, j.specialty, j.skills, j.created_at, j.job_type,
                DATE_FORMAT(j.created_at, '%b %d, %Y') as created_at_formatted,
                c.first_name, c.last_name, c.country, c.position, c.profile_photo,
                c.business_name, c.business_website, c.industry
            FROM job_submissions j
            LEFT JOIN approve_client c ON j.client_id = c.id
            WHERE j.id = %s
        """
        cursor.execute(job_query, (job_id,))
        job = cursor.fetchone()

        if not job:
            # Check if error.html exists, if not use a simple message
            try:
                return render_template('error.html', message="Job not found"), 404
            except:
                return "Job not found", 404

        # Handle profile photo if it exists
        if job['profile_photo']:
            client_photo_url = f"data:image/jpeg;base64,{base64.b64encode(job['profile_photo']).decode()}"
        else:
            client_photo_url = url_for('static', filename='img/default_profile.png')

        # Add location field expected by the template
        job['location'] = job['country']

        # Parse skills if they exist
        skills = []
        if job['skills'] and job['skills'].strip():
            try:
                # Try to parse as JSON first
                import json
                skills = json.loads(job['skills'])
            except:
                # If not JSON, try comma-separated string
                skills = [skill.strip() for skill in job['skills'].split(',') if skill.strip()]

        # Create a client object with additional information
        client = {
            'id': job['client_id'],
            'first_name': job['first_name'],
            'last_name': job['last_name'],
            'business_name': job['business_name'],
            'position': job['position'],
            'country': job['country'],
            'industry': job['industry'],
            'website': job['business_website'],
            'photo_url': client_photo_url
        }

        # Check if the user has already applied for this job (only for genius users)
        has_applied = False
        if session.get('user_type') == 'genius':
            cursor.execute("""
                SELECT id FROM applications
                WHERE genius_id = %s AND job_id = %s
            """, (session.get('user_id'), job_id))
            has_applied = cursor.fetchone() is not None

        cursor.close()
        conn.close()

        # Use job_details.html template with enhanced client information
        return render_template('job_details.html', job=job, client=client,
                              client_photo_url=client_photo_url, skills=skills,
                              has_applied=has_applied)

    except Exception as e:
        print(f"Error fetching job submission details: {e}")
        # Check if error.html exists, if not use a simple message
        try:
            return render_template('error.html', message="Error fetching job details"), 500
        except:
            return f"Error fetching job details: {str(e)}", 500

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('landing_page'))

@app.route('/check_email', methods=['POST'])
def check_email():
    try:
        email = request.json.get('email')
        registration_type = request.json.get('type', 'genius')

        if not email:
            return jsonify({'error': 'Email is required'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        if registration_type == 'client':
            # Check in both register_client and approve_client tables
            cursor.execute("""
                SELECT work_email FROM register_client WHERE work_email = %s
                UNION
                SELECT work_email FROM approve_client WHERE work_email = %s
            """, (email, email))
        else:
            # Check in both register_genius and approve_genius tables
            cursor.execute("""
                SELECT email FROM register_genius WHERE email = %s
                UNION
                SELECT email FROM approve_genius WHERE email = %s
            """, (email, email))

        result = cursor.fetchone()

        cursor.close()
        conn.close()

        return jsonify({'exists': bool(result)})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/submit_contact', methods=['POST'])
def submit_contact():
    try:
        conn = get_db_connection()
        if conn is None:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            })

        cursor = conn.cursor()

        # Get form data
        name = request.form.get('name')
        email = request.form.get('email')
        subject = request.form.get('subject')
        message = request.form.get('message')

        # Get additional info
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent')

        # Insert into database with proper error handling
        try:
            sql = """
            INSERT INTO contact_us_messages
            (full_name, email, subject, message, ip_address, user_agent, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, NOW())
            """

            cursor.execute(sql, (name, email, subject, message, ip_address, user_agent))
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'Message sent successfully'
            })

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': 'Failed to save message'
            }), 500

    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'An unexpected error occurred'
        }), 500

    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

# Update the affiliate_register route
@app.route('/affiliate_register', methods=['POST'])
def affiliate_register():
    try:
        # Get form data
        first_name = request.form.get('firstName', '').strip()
        last_name = request.form.get('lastName', '').strip()
        phone = request.form.get('phone', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '').strip()

        # Validate required fields
        if not all([first_name, last_name, phone, email, password]):
            return jsonify({
                'success': False,
                'error': 'All fields are required'
            }), 400

        # Generate unique referral code
        referral_code = generate_referral_code(first_name, last_name)

        # Insert into database
        conn = get_db_connection()
        if conn is None:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500

        cursor = conn.cursor()

        try:
            # Check if email already exists
            cursor.execute("SELECT email FROM affiliates WHERE email = %s", (email,))
            if cursor.fetchone():
                return jsonify({
                    'success': False,
                    'error': 'Email already registered'
                }), 400

            # Insert new affiliate
            sql = """
            INSERT INTO affiliates (
                first_name, last_name, phone, email, password,
                referral_code, status, commission_rate_freelancer,
                commission_rate_client, total_earnings
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor.execute(sql, (
                first_name,
                last_name,
                phone,
                email,
                password,
                referral_code,
                'pending',
                0.50,  # Default commission for freelancers
                1.00,  # Default commission for clients
                0.00   # Initial total earnings
            ))

            conn.commit()
            return jsonify({
                'success': True,
                'message': 'Registration successful! Your application is under review.'
            })

        except mysql.connector.Error as err:
            conn.rollback()
            print(f"Database Error: {err}")
            return jsonify({
                'success': False,
                'error': 'Database error occurred'
            }), 500

        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Registration failed'
        }), 500

# Update the affiliate_login route to check status
@app.route('/affiliate_login', methods=['POST'])
def affiliate_login():
    try:
        email = request.form.get('email')
        password = request.form.get('password')

        if not email or not password:
            return jsonify({
                'success': False,
                'error': 'Email and password are required'
            }), 400

        conn = get_db_connection()
        if conn is None:
            return jsonify({
                'success': False,
                'error': 'Database connection failed'
            }), 500

        cursor = conn.cursor(dictionary=True)

        try:
            cursor.execute("""
                SELECT id, first_name, last_name, email, status, referral_code
                FROM affiliates
                WHERE email = %s AND password = %s
            """, (email, password))

            affiliate = cursor.fetchone()

            if not affiliate:
                return jsonify({
                    'success': False,
                    'error': 'Invalid email or password'
                }), 401

            if affiliate['status'] != 'approved':
                return jsonify({
                    'success': False,
                    'error': 'Your account is pending approval'
                }), 403

            # Create session
            session['affiliate_id'] = affiliate['id']
            session['affiliate_name'] = f"{affiliate['first_name']} {affiliate['last_name']}"
            session['affiliate_email'] = affiliate['email']
            session['affiliate_code'] = affiliate['referral_code']

            return jsonify({
                'success': True,
                'redirect': '/affiliate_dashboard'
            })

        except Exception as e:
            print(f"Login error: {str(e)}")
            return jsonify({
                'success': False,
                'error': 'An error occurred during login'
            }), 500

        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        print(f"Error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'An unexpected error occurred'
        }), 500

# Add route to update affiliate status
@app.route('/update_affiliate_status', methods=['POST'])
def update_affiliate_status():
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    data = request.json
    affiliate_id = data.get('affiliate_id')
    status = data.get('status')

    if not affiliate_id or not status:
        return jsonify({'error': 'Missing affiliate_id or status'}), 400

    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # Update the status in the affiliates table
        cursor.execute("UPDATE affiliates SET status = %s WHERE id = %s",
                     (status, affiliate_id))

        conn.commit()
        return jsonify({'success': True})

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        conn.rollback()
        return jsonify({'error': str(err)}), 500

    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
        return jsonify({'error': str(e)}), 500

    finally:
        cursor.close()
        conn.close()

# Add route to get affiliate details
@app.route('/get_affiliate_details/<int:id>')
def get_affiliate_details(id):
    if 'user_id' not in session or session.get('user_type') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        cursor.execute("""
            SELECT
                id,
                first_name,
                last_name,
                email,
                phone,
                referral_code,
                status,
                commission_rate_freelancer,
                commission_rate_client,
                total_earnings,
                DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') as created_at
            FROM affiliates
            WHERE id = %s
        """, (id,))

        affiliate = cursor.fetchone()

        if affiliate:
            # Handle binary data separately if needed
            cursor.execute("SELECT profile_photo FROM affiliates WHERE id = %s", (id,))
            binary_data = cursor.fetchone()

            # Convert binary data to base64 if exists
            if binary_data and binary_data.get('profile_photo'):
                affiliate['profile_photo'] = f"data:image/jpeg;base64,{base64.b64encode(binary_data['profile_photo']).decode()}"

            return jsonify(affiliate)

        return jsonify({'error': 'Affiliate not found'}), 404

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        return jsonify({'error': 'Database error occurred'}), 500

    finally:
        cursor.close()
        conn.close()

def generate_referral_code(first_name, last_name):
    """Generate a unique referral code based on name and timestamp"""
    import random
    import string
    import time

    # Get first 2 letters of first name and last name (or less if names are shorter)
    first_prefix = first_name[:2].upper() if len(first_name) >= 2 else first_name.upper()
    last_prefix = last_name[:2].upper() if len(last_name) >= 2 else last_name.upper()

    # Add timestamp and random characters
    timestamp = str(int(time.time()))[-4:]
    random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))

    # Combine to create referral code
    referral_code = f"{first_prefix}{last_prefix}{timestamp}{random_chars}"

    return referral_code

# News and Events functions
@app.route('/admin/news/create', methods=['POST'])
def create_news():
    if request.method == 'POST':
        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Get form data
            title = request.form.get('title')
            content = request.form.get('content')
            image = request.files.get('image')
            event_date = request.form.get('event_date')
            category = request.form.get('category')

            # Handle image upload if provided
            image_path = None
            if image and image.filename:
                filename = secure_filename(image.filename)
                image_path = f'static/uploads/news/{filename}'
                image.save(os.path.join(app.root_path, image_path))

            # Insert into database
            sql = """
            INSERT INTO news_events
            (title, content, image_path, event_date, author_id, category)
            VALUES (%s, %s, %s, %s, %s, %s)
            """

            author_id = session.get('user_id', None)
            cursor.execute(sql, (title, content, image_path, event_date, author_id, category))
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'News/event created successfully'
            })

        except mysql.connector.Error as err:
            if conn:
                conn.rollback()
            print(f"Database Error: {err}")
            return jsonify({
                'success': False,
                'error': f'Database error: {str(err)}'
            }), 500

        finally:
            if 'cursor' in locals():
                cursor.close()
            if conn:
                conn.close()

    return render_template('admin_news_create.html')

@app.route('/admin/news/delete/<int:news_id>', methods=['POST'])
def delete_news(news_id):
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Delete the news/event
        cursor.execute("DELETE FROM news_events WHERE id = %s", (news_id,))
        conn.commit()

        return jsonify({
            'success': True,
            'message': 'News/event deleted successfully'
        })

    except mysql.connector.Error as err:
        if conn:
            conn.rollback()
        return jsonify({
            'success': False,
            'error': f'Database error: {str(err)}'
        }), 500

    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

@app.route('/create_post', methods=['POST'])
def create_post():
    if request.method == 'POST':
        conn = None
        cursor = None
        try:
            # Debug print to see what's being received
            print("Form data received:", request.form)
            print("Files received:", list(request.files.keys()))

            # Validate required fields
            title = request.form.get('title')
            content = request.form.get('content')
            category = request.form.get('category')
            audience = request.form.get('audience')

            if not title or not content:
                return jsonify({
                    'success': False,
                    'error': 'Title and content are required'
                }), 400

            print(f"Processing post: {title}, {category}, {audience}")

            # Get database connection
            conn = get_db_connection()
            if conn is None:
                print("Database connection failed")
                return jsonify({
                    'success': False,
                    'error': 'Database connection failed'
                }), 500

            cursor = conn.cursor()

            # Handle image upload
            image_path = None
            if 'image' in request.files:
                image = request.files['image']
                if image.filename != '':
                    try:
                        filename = secure_filename(image.filename)
                        # Make sure the upload directory exists
                        upload_dir = os.path.join(app.root_path, 'static/uploads/posts')
                        if not os.path.exists(upload_dir):
                            os.makedirs(upload_dir)

                        image_path = f'static/uploads/posts/{filename}'
                        full_path = os.path.join(app.root_path, image_path)
                        image.save(full_path)
                        print(f"Image saved to {full_path}")
                    except Exception as img_err:
                        print(f"Image upload error: {str(img_err)}")
                        return jsonify({
                            'success': False,
                            'error': f'Image upload failed: {str(img_err)}'
                        }), 500

            # Get author info from session if available
            author_id = session.get('user_id', None)
            print(f"Author ID: {author_id}")

            # Insert into database
            sql = """
            INSERT INTO news_events
            (title, content, image_path, category, audience, author_id, is_published)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """

            data = (title, content, image_path, category, audience, author_id, 1)
            print(f"SQL data: {data}")

            cursor.execute(sql, data)
            conn.commit()

            return jsonify({
                'success': True,
                'message': 'Post created successfully'
            })

        except mysql.connector.Error as err:
            print(f"Database Error: {err}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Database error: {str(err)}'
            }), 500

        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            if conn:
                conn.rollback()
            return jsonify({
                'success': False,
                'error': f'Unexpected error: {str(e)}'
            }), 500

        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    return jsonify({
        'success': False,
        'error': 'Invalid request method'
    }), 405

# Add this function to check if the news_events table exists
def ensure_news_events_table():
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if table exists
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'news_events'
        """)

        if cursor.fetchone()[0] == 0:
            # Table doesn't exist, create it
            cursor.execute("""
                CREATE TABLE news_events (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    content TEXT NOT NULL,
                    image_path VARCHAR(255),
                    event_date DATETIME,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    is_published TINYINT(1) DEFAULT 1,
                    author_id INT,
                    category VARCHAR(50),
                    audience VARCHAR(50)
                )
            """)
            conn.commit()
            print("Created news_events table")
        else:
            # Check if audience column exists
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_schema = DATABASE()
                AND table_name = 'news_events'
                AND column_name = 'audience'
            """)

            if cursor.fetchone()[0] == 0:
                # Add audience column
                cursor.execute("""
                    ALTER TABLE news_events
                    ADD COLUMN audience VARCHAR(50) AFTER category
                """)
                conn.commit()
                print("Added audience column to news_events table")

        print("news_events table is ready")

    except mysql.connector.Error as err:
        print(f"Database Error during table check: {err}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

# Add this function to check if the applications table exists
def ensure_applications_table():
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if table exists
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'applications'
        """)

        table_exists = cursor.fetchone()[0] > 0

        # If table exists, check if it has the job_id column
        if table_exists:
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_schema = DATABASE()
                AND table_name = 'applications'
                AND column_name = 'job_id'
            """)

            job_id_exists = cursor.fetchone()[0] > 0

            # If job_id column doesn't exist, drop and recreate the table
            if not job_id_exists:
                print("Applications table exists but missing job_id column. Recreating table...")
                cursor.execute("DROP TABLE applications")
                conn.commit()
                table_exists = False

        if not table_exists:
            # Table doesn't exist, create it
            cursor.execute("""
                CREATE TABLE applications (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    genius_id INT NOT NULL,
                    client_id INT NOT NULL,
                    job_id INT NOT NULL,
                    first_name VARCHAR(100) NOT NULL,
                    last_name VARCHAR(100) NOT NULL,
                    profile_photo LONGBLOB,
                    position VARCHAR(100),
                    status VARCHAR(20) DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX (genius_id),
                    INDEX (client_id),
                    INDEX (job_id),
                    UNIQUE KEY unique_application (genius_id, job_id)
                )
            """)
            conn.commit()
            print("Created applications table")

        print("applications table is ready")

    except mysql.connector.Error as err:
        print(f"Database Error during applications table check: {err}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

# Add this function to check if the portfolio table exists
def ensure_portfolio_table():
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if table exists
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'portfolio'
        """)

        if cursor.fetchone()[0] == 0:
            # Table doesn't exist, create it
            cursor.execute("""
                CREATE TABLE portfolio (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    genius_id INT NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    provider VARCHAR(255) NOT NULL,
                    receiver VARCHAR(255) NOT NULL,
                    project_date DATE NOT NULL,
                    description TEXT NOT NULL,
                    image_path VARCHAR(500),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX (genius_id),
                    FOREIGN KEY (genius_id) REFERENCES approve_genius(id) ON DELETE CASCADE
                )
            """)
            conn.commit()
            print("Created portfolio table")

        print("portfolio table is ready")

    except mysql.connector.Error as err:
        print(f"Database Error during portfolio table check: {err}")
    finally:
        if 'cursor' in locals():
            cursor.close()
        if conn:
            conn.close()

# VIEW JOB DETAILS ROUTE
@app.route('/view_job/<int:job_id>')
def view_job(job_id):
    if 'user_id' not in session:
        return redirect(url_for('landing_page'))

    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)

        # Get job details with client information directly from job_submissions
        cursor.execute("""
            SELECT
                j.*,
                DATE_FORMAT(j.created_at, '%b %d, %Y') as created_at_formatted
            FROM job_submissions j
            WHERE j.id = %s
        """, (job_id,))

        job = cursor.fetchone()

        if not job:
            return redirect(url_for('genius_page'))

        # Get client details - now we can use the data directly from job_submissions
        client = {
            'id': job['client_id'],
            'first_name': job['first_name'],
            'last_name': job['last_name'],
            'country': job['country'],
            'position': job['position']
        }

        # Handle profile photo if it exists
        if job['profile_photo']:
            client['profile_photo_url'] = f"data:image/jpeg;base64,{base64.b64encode(job['profile_photo']).decode()}"
        else:
            client['profile_photo_url'] = url_for('static', filename='img/default_profile.png')

        # Get current user type
        user_type = session.get('user_type')

        return render_template('view_job.html', job=job, client=client, user_type=user_type)

    except mysql.connector.Error as err:
        print(f"Database Error: {err}")
        return redirect(url_for('genius_page'))
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()





@app.route('/page1')
def page1():
    return render_template('page1.html')

# Portfolio routes
@app.route('/add_portfolio', methods=['POST'])
@login_required
def add_portfolio():
    """Add a new portfolio item"""
    if session.get('user_type') != 'genius':
        return jsonify({'success': False, 'error': 'Unauthorized'}), 403

    try:
        # Get form data
        title = request.form.get('title', '').strip()
        provider = request.form.get('provider', '').strip()
        receiver = request.form.get('receiver', '').strip()
        project_date = request.form.get('date', '').strip()
        description = request.form.get('description', '').strip()

        # Validate required fields
        if not all([title, provider, receiver, project_date, description]):
            return jsonify({'success': False, 'error': 'All fields are required'}), 400

        # Validate description length
        if len(description) < 300:
            return jsonify({'success': False, 'error': 'Description must be at least 300 characters'}), 400

        # Handle file upload
        image_path = None
        if 'portfolio_image' in request.files:
            file = request.files['portfolio_image']
            if file and file.filename:
                # Validate file type
                allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
                if '.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions:
                    # Create uploads directory if it doesn't exist
                    upload_dir = os.path.join(app.static_folder, 'uploads', 'portfolio')
                    os.makedirs(upload_dir, exist_ok=True)

                    # Generate unique filename
                    filename = f"{session['user_id']}_{int(time.time())}_{secure_filename(file.filename)}"
                    file_path = os.path.join(upload_dir, filename)
                    file.save(file_path)

                    # Store relative path for database
                    image_path = f"uploads/portfolio/{filename}"
                else:
                    return jsonify({'success': False, 'error': 'Invalid file type. Please upload PNG, JPG, JPEG, or GIF files.'}), 400

        if not image_path:
            return jsonify({'success': False, 'error': 'Portfolio image is required'}), 400

        # Insert into database
        conn = get_db_connection()
        cursor = conn.cursor()

        query = """
            INSERT INTO portfolio (genius_id, title, provider, receiver, project_date, description, image_path)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """

        cursor.execute(query, (
            session['user_id'],
            title,
            provider,
            receiver,
            project_date,
            description,
            image_path
        ))

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({'success': True, 'message': 'Portfolio item added successfully'})

    except Exception as e:
        print(f"Error adding portfolio: {str(e)}")
        return jsonify({'success': False, 'error': 'An error occurred while adding the portfolio item'}), 500

@app.route('/edit_portfolio', methods=['POST'])
@login_required
def edit_portfolio():
    """Edit an existing portfolio item"""
    if session.get('user_type') != 'genius':
        return jsonify({'success': False, 'error': 'Unauthorized'}), 403

    try:
        # Get form data
        portfolio_id = request.form.get('portfolio_id')
        title = request.form.get('title', '').strip()
        provider = request.form.get('provider', '').strip()
        receiver = request.form.get('receiver', '').strip()
        project_date = request.form.get('date', '').strip()
        description = request.form.get('description', '').strip()

        # Validate required fields
        if not all([title, provider, receiver, project_date, description]):
            return jsonify({'success': False, 'error': 'All fields are required'}), 400

        # Validate description length
        if len(description) < 300:
            return jsonify({'success': False, 'error': 'Description must be at least 300 characters'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # Handle file upload if new image is provided
        image_path = None
        if 'portfolio_image' in request.files:
            file = request.files['portfolio_image']
            if file and file.filename:
                # Validate file type
                allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
                if '.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions:
                    # Create uploads directory if it doesn't exist
                    upload_dir = os.path.join(app.static_folder, 'uploads', 'portfolio')
                    os.makedirs(upload_dir, exist_ok=True)

                    # Generate unique filename
                    filename = f"{session['user_id']}_{int(time.time())}_{secure_filename(file.filename)}"
                    file_path = os.path.join(upload_dir, filename)
                    file.save(file_path)

                    # Store relative path for database
                    image_path = f"uploads/portfolio/{filename}"
                else:
                    return jsonify({'success': False, 'error': 'Invalid file type. Please upload PNG, JPG, JPEG, or GIF files.'}), 400

        # Update database
        if image_path:
            # Update with new image
            query = """
                UPDATE portfolio
                SET title = %s, provider = %s, receiver = %s, project_date = %s, description = %s, image_path = %s
                WHERE genius_id = %s
            """
            cursor.execute(query, (title, provider, receiver, project_date, description, image_path, session['user_id']))
        else:
            # Update without changing image
            query = """
                UPDATE portfolio
                SET title = %s, provider = %s, receiver = %s, project_date = %s, description = %s
                WHERE genius_id = %s
            """
            cursor.execute(query, (title, provider, receiver, project_date, description, session['user_id']))

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({'success': True, 'message': 'Portfolio item updated successfully'})

    except Exception as e:
        print(f"Error editing portfolio: {str(e)}")
        return jsonify({'success': False, 'error': 'An error occurred while updating the portfolio item'}), 500

@app.route('/save_page1', methods=['POST'])
def save_page1():
    session['job_type'] = request.form.get('job_type')

    # Check if project_size is in the form and save it to session
    if 'project_size' in request.form:
        session['project_size'] = request.form.get('project_size')
        print(f"Project size from page1: {session['project_size']}")

    return redirect(url_for('page2'))

@app.route('/page2')
def page2():
    return render_template('page2_new.html')

@app.route('/save_page2', methods=['POST'])
def save_page2():
    # Store the data in session with better error handling
    title = request.form.get('title', '')
    category = request.form.get('job_category', '')
    specialty = request.form.get('specialty', '')

    # Debug print to see what's being received
    print(f"Received from form - title: {title}, category: {category}, specialty: {specialty}")

    # Only set default if category is empty
    if not category:
        print("Warning: No category received, using default")
        category = "Writing"  # Change this to your preferred default

    session['title'] = title
    session['job_category'] = category

    # Save specialty if provided
    if specialty:
        session['specialty'] = specialty
        print(f"Saved specialty to session: {specialty}")

    print(f"Saved to session - title: {session['title']}, category: {session['job_category']}, specialty: {session.get('specialty', 'Not set')}")

    return redirect(url_for('page3'))

@app.route('/page3')
def page3():
    # Check if session data exists for title and job_category
    if 'title' not in session or 'job_category' not in session:
        return redirect(url_for('page2'))  # Redirect to page2 if data is missing

    # Get specialty from session if it exists
    specialty = session.get('specialty', 'General')

    # Pass the session data to the template for rendering on page3
    return render_template('page3.html',
                         title=session['title'],
                         job_category=session['job_category'],
                         specialty=specialty)

@app.route('/submit_job', methods=['POST'])
def submit_job():
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'Not logged in'})

    conn = None
    cursor = None

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # First, get client information from approve_client table
        client_id = session.get('user_id')
        cursor.execute("""
            SELECT first_name, last_name, country, position, profile_photo
            FROM approve_client
            WHERE id = %s
        """, (client_id,))

        client_info = cursor.fetchone()

        if not client_info:
            return jsonify({
                'success': False,
                'error': 'Client information not found'
            }), 404

        # Extract form data with proper error handling
        title = request.form.get('title', '')
        description = request.form.get('description', '')
        category = request.form.get('category', '')
        specialty = request.form.get('specialty', '')
        skills = request.form.get('skills', '[]')

        # Extract scope details
        project_size = request.form.get('project_size', '')
        project_description = request.form.get('project_description', '')
        duration = request.form.get('duration', '')
        experience_level = request.form.get('experience_level', '')
        hiring_preference = request.form.get('hiring_preference', '')

        # Extract budget details
        budget_type = request.form.get('budget_type', 'fixed')
        budget_amount = request.form.get('budget_amount', '0')

        # Set job type from session
        job_type = session.get('job_type', 'Remote')

        # Debug print for extracted fields
        print(f"Extracted fields for job submission:")
        print(f"Client ID: {client_id}")
        print(f"Client Name: {client_info['first_name']} {client_info['last_name']}")
        print(f"Title: {title}")
        print(f"Description: {description}")
        print(f"Category: {category}")
        print(f"Specialty: {specialty}")

        # Use a query that includes the new client fields
        query = """
            INSERT INTO job_submissions (
                client_id,
                first_name,
                last_name,
                country,
                position,
                profile_photo,
                title,
                description,
                project_size,
                project_description,
                experience_level,
                duration,
                hiring_preference,
                budget_type,
                budget_amount,
                category,
                specialty,
                skills,
                created_at,
                job_type
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s
            )
        """

        # Ensure values match the order in the query
        values = (
            client_id,
            client_info['first_name'],
            client_info['last_name'],
            client_info['country'],
            client_info['position'],
            client_info['profile_photo'],
            title,
            description,
            project_size,
            project_description,
            experience_level,
            duration,
            hiring_preference,
            budget_type,
            budget_amount,
            category,
            specialty,
            skills,
            job_type
        )

        # Debug print with detailed information (excluding binary data)
        print(f"Executing SQL with values: {[v if not isinstance(v, bytes) else 'binary_data' for v in values]}")

        cursor.execute(query, values)
        job_id = cursor.lastrowid
        conn.commit()
        print(f"Successfully inserted job with ID: {job_id}")

        # Clear session data after successful submission
        session.pop('job_type', None)
        session.pop('title', None)
        session.pop('job_category', None)
        session.pop('specialty', None)
        session.pop('project_size', None)

        return jsonify({
            'success': True,
            'message': 'Job submitted successfully',
            'job_id': job_id,
            'redirect': url_for('client_page')
        })

    except mysql.connector.Error as err:
        print(f"Database error during job submission: {str(err)}")
        if conn:
            conn.rollback()
        return jsonify({
            'success': False,
            'error': f'Database error: {str(err)}'
        }), 500

    except Exception as e:
        print(f"Unexpected error during job submission: {str(e)}")
        if conn:
            conn.rollback()
        return jsonify({
            'success': False,
            'error': f'Unexpected error: {str(e)}'
        }), 500

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/submit_application', methods=['POST'])
def submit_application():
    """Handle job application submission"""
    if 'user_id' not in session or session.get('user_type') != 'genius':
        return jsonify({'success': False, 'error': 'Not logged in as genius'}), 401

    try:
        # Get data from request
        job_id = request.form.get('job_id')
        client_id = request.form.get('client_id')

        if not job_id or not client_id:
            return jsonify({'success': False, 'error': 'Missing required fields'}), 400

        # Get genius information
        genius_id = session.get('user_id')

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Check if application already exists
        cursor.execute("""
            SELECT id FROM applications
            WHERE genius_id = %s AND job_id = %s
        """, (genius_id, job_id))

        if cursor.fetchone():
            return jsonify({
                'success': False,
                'error': 'You have already applied for this job'
            }), 400

        # Get genius information
        cursor.execute("""
            SELECT first_name, last_name, profile_photo, position
            FROM approve_genius
            WHERE id = %s
        """, (genius_id,))

        genius_info = cursor.fetchone()

        if not genius_info:
            return jsonify({
                'success': False,
                'error': 'Genius information not found'
            }), 404

        # Insert application
        cursor.execute("""
            INSERT INTO applications (
                genius_id, client_id, job_id, first_name, last_name,
                profile_photo, position, status, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW())
        """, (
            genius_id,
            client_id,
            job_id,
            genius_info['first_name'],
            genius_info['last_name'],
            genius_info['profile_photo'],
            genius_info['position'],
            'pending'
        ))

        conn.commit()
        application_id = cursor.lastrowid

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'Application submitted successfully',
            'application_id': application_id
        })

    except Exception as e:
        print(f"Error submitting application: {e}")
        return jsonify({
            'success': False,
            'error': f'Error: {str(e)}'
        }), 500


@app.route('/view_job_submission/<int:job_id>')
def view_job_submission(job_id):
    """Display detailed information about a specific job submission"""
    if 'user_id' not in session:
        return redirect(url_for('landing_page'))

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get job details from job_submissions table
        job_query = """
            SELECT
                id, client_id, first_name, last_name, country, position, profile_photo,
                title, description, project_size, project_description, duration,
                experience_level, hiring_preference, budget_type, budget_amount,
                category, specialty, skills, created_at, job_type,
                DATE_FORMAT(created_at, '%b %d, %Y') as created_at_formatted
            FROM job_submissions
            WHERE id = %s
        """
        cursor.execute(job_query, (job_id,))
        job = cursor.fetchone()

        if not job:
            # Check if error.html exists, if not use a simple message
            try:
                return render_template('error.html', message="Job not found"), 404
            except:
                return "Job not found", 404

        # Handle profile photo if it exists
        if job['profile_photo']:
            client_photo_url = f"data:image/jpeg;base64,{base64.b64encode(job['profile_photo']).decode()}"
        else:
            client_photo_url = url_for('static', filename='img/default_profile.png')

        # Add location field expected by the template
        job['location'] = job['country']

        # Parse skills if they exist
        skills = []
        if job['skills'] and job['skills'].strip():
            try:
                # Try to parse as JSON first
                import json
                skills = json.loads(job['skills'])
            except:
                # If not JSON, try comma-separated string
                skills = [skill.strip() for skill in job['skills'].split(',') if skill.strip()]

        cursor.close()
        conn.close()

        # Use job_details.html template
        return render_template('job_details.html', job=job, client_photo_url=client_photo_url, skills=skills)

    except Exception as e:
        print(f"Error fetching job submission details: {e}")
        # Check if error.html exists, if not use a simple message
        try:
            return render_template('error.html', message="Error fetching job details"), 500
        except:
            return f"Error fetching job details: {str(e)}", 500





@app.route('/handle-application/<int:application_id>/<action>', methods=['POST'])
def handle_application_status(application_id, action):
    if action not in ['accept', 'reject']:
        return jsonify({'success': False, 'error': 'Invalid action'}), 400

    try:
        connection = get_db_connection()
        cursor = connection.cursor(dictionary=True)  # Make sure to use dictionary=True

        # First get the genius_id before updating status
        cursor.execute("""
            SELECT genius_id FROM applications
            WHERE id = %s AND client_id = %s
        """, (application_id, session['user_id']))

        result = cursor.fetchone()
        if not result:
            return jsonify({'success': False, 'error': 'Application not found'}), 404

        genius_id = result['genius_id']  # Access as dictionary key

        # Update application status - use the exact action value
        cursor.execute("""
            UPDATE applications
            SET status = %s,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
            AND client_id = %s
        """, (action, application_id, session['user_id']))

        # If accepted, send welcome message from client to genius
        if action == 'accept':
            cursor.execute("""
                INSERT INTO messages
                (sender_id, receiver_id, message_text, status, timestamp)
                VALUES
                (%s, %s, %s, 'sent', CURRENT_TIMESTAMP)
            """, (
                session['user_id'],  # client is sending the message
                genius_id,           # to the genius
                'Thank you for your application. I look forward to working with you! 👋'
            ))

        connection.commit()
        cursor.close()  # Close the cursor
        return jsonify({'success': True})

    except Exception as e:
        print(f"Error handling application: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500
    finally:
        connection.close()

@app.route('/get_client_notifications')
@login_required
def get_client_notifications():
    if session.get('user_type') != 'client':
        return jsonify({'success': False, 'error': 'Not authorized'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        client_id = session.get('user_id')
        print(f"Fetching notifications for client ID: {client_id}")

        # Get applications for this client with more detailed query
        cursor.execute("""
            SELECT
                a.id,
                a.genius_id,
                a.job_id,
                a.first_name,
                a.last_name,
                a.position,
                a.status,
                a.created_at,
                j.title as job_title
            FROM applications a
            JOIN job_submissions j ON a.job_id = j.id
            WHERE a.client_id = %s
            ORDER BY
                CASE
                    WHEN a.status = 'pending' THEN 0
                    WHEN a.status = 'accept' THEN 1
                    WHEN a.status = 'reject' THEN 2
                    ELSE 3
                END,
                a.created_at DESC
            LIMIT 20
        """, (client_id,))

        notifications = cursor.fetchall()
        print(f"Found {len(notifications)} notifications")

        # Convert datetime objects to strings for JSON serialization
        for notification in notifications:
            if isinstance(notification['created_at'], datetime):
                notification['created_at'] = notification['created_at'].strftime('%Y-%m-%d %H:%M:%S')

        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'notifications': notifications
        })

    except Exception as e:
        print(f"Error fetching notifications: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })




@app.route('/my_proposal')
@login_required
def my_proposal():
    if 'user_id' in session and session.get('user_type') == 'genius':
        # Fetch genius data from the database
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)

            # Get profile data
            profile_query = """
                SELECT position, country, hourly_rate, profile_photo, first_name, last_name, introduction
                FROM approve_genius
                WHERE id = %s
            """
            cursor.execute(profile_query, (session.get('user_id'),))
            profile_data = cursor.fetchone()

            cursor.close()
            conn.close()

            # Create genius object with profile data
            genius = {
                'position': profile_data.get('position', 'Not specified'),
                'country': profile_data.get('country', 'Not specified'),
                'hourly_rate': profile_data.get('hourly_rate', 0),
                'first_name': profile_data.get('first_name', ''),
                'last_name': profile_data.get('last_name', ''),
                'introduction': profile_data.get('introduction', 'No introduction provided.')
            }

            # Handle profile photo
            if profile_data and profile_data.get('profile_photo'):
                genius['profile_picture_url'] = f"data:image/jpeg;base64,{base64.b64encode(profile_data['profile_photo']).decode()}"
            else:
                genius['profile_picture_url'] = url_for('static', filename='img/default_profile.png')

            # You can add logic here to fetch the genius's proposals from the database
            return render_template('My_proposal.html', genius=genius)

        except Exception as e:
            print(f"Error fetching genius profile data: {e}")
            # Provide default genius data in case of error
            genius = {
                'position': 'Not specified',
                'country': 'Not specified',
                'hourly_rate': 0,
                'first_name': session.get('name', '').split()[0] if session.get('name') else '',
                'last_name': session.get('name', '').split()[-1] if session.get('name') else '',
                'introduction': 'No introduction provided.',
                'profile_picture_url': url_for('static', filename='img/default_profile.png')
            }
            return render_template('My_proposal.html', genius=genius)
    return redirect(url_for('landing_page'))

@app.route('/tracker')
@login_required
def tracker():
    if 'user_id' in session and session.get('user_type') == 'genius':
        # You can add logic here to fetch the genius's time tracking data from the database
        return render_template('Tracker.html')
    return redirect(url_for('landing_page'))

@app.route('/billing_and_earnings')
@login_required
def billing_and_earnings():
    if 'user_id' in session and session.get('user_type') == 'genius':
        # You can add logic here to fetch the genius's billing and earnings data from the database
        return render_template('Billing_and_Earnings.html')
    return redirect(url_for('landing_page'))

@app.route('/withdraw_earnings')
@login_required
def withdraw_earnings():
    if 'user_id' in session and session.get('user_type') == 'genius':
        # Get the section parameter from the URL
        section = request.args.get('section', 'get-paid')  # Default to 'get-paid'
        # You can add logic here to fetch the genius's withdrawal data from the database
        return render_template('Withdraw_Earnings.html', active_section=section)
    return redirect(url_for('landing_page'))

@app.route('/tax_info')
@login_required
def tax_info():
    if 'user_id' in session and session.get('user_type') == 'genius':
        # You can add logic here to fetch the genius's tax information from the database
        return render_template('Tax_info.html')
    return redirect(url_for('landing_page'))

@app.route('/messages')
def messages():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        connection = get_db_connection()
        if connection is None:
            return "Error: Could not connect to database", 500

        cursor = connection.cursor(dictionary=True)

        current_user_type = session.get('user_type')
        current_user_id = session.get('user_id')

        # Updated query to fix the ORDER BY with DISTINCT issue
        if current_user_type == 'genius':
            query = """
                WITH LatestActivity AS (
                    SELECT
                        contact_id,
                        message_text,
                        sender_id,
                        activity_time,
                        activity_type,
                        emoji,
                        original_sender_id,
                        ROW_NUMBER() OVER (PARTITION BY contact_id ORDER BY sort_time DESC) as rn,
                        message_type,
                        file_name
                    FROM (
                        SELECT
                            CASE
                                WHEN m.sender_id = %s THEN m.receiver_id
                                ELSE m.sender_id
                            END as contact_id,
                            m.message_text,
                            m.sender_id,
                            m.timestamp as activity_time,
                            'message' as activity_type,
                            NULL as emoji,
                            NULL as original_sender_id,
                            m.timestamp as sort_time,
                            m.message_type,
                            m.file_name
                        FROM messages m
                        WHERE m.sender_id = %s OR m.receiver_id = %s

                        UNION ALL

                        SELECT
                            r.user_id as contact_id,
                            NULL as message_text,
                            r.user_id as sender_id,
                            r.created_at as activity_time,
                            'reaction' as activity_type,
                            r.emoji,
                            m.sender_id as original_sender_id,
                            r.created_at as sort_time,
                            m.message_type,
                            m.file_name
                        FROM reactions r
                        JOIN messages m ON r.message_id = m.id
                        WHERE (m.sender_id = %s OR m.receiver_id = %s)
                            AND r.user_type = 'client'
                    ) all_activities
                )
                SELECT
                    c.id,
                    c.first_name,
                    c.last_name,
                    c.profile_photo,
                    'client' as user_type,
                    CASE
                        WHEN la.activity_type = 'reaction' AND la.original_sender_id = %s
                            THEN CONCAT(c.first_name, ' reacted ', la.emoji, ' to your message')
                        ELSE la.message_text
                    END as last_message,
                    COALESCE(la.sender_id, 0) as last_message_sender_id,
                    COALESCE(la.activity_time, NOW()) as last_activity_time,
                    la.message_type as last_message_type,
                    la.file_name as last_file_name
                FROM approve_client c
                LEFT JOIN LatestActivity la ON la.contact_id = c.id AND la.rn = 1
                WHERE EXISTS (
                    SELECT 1 FROM messages
                    WHERE (sender_id = %s AND receiver_id = c.id)
                    OR (sender_id = c.id AND receiver_id = %s)
                )
                ORDER BY last_activity_time DESC
            """
        else:  # client
            query = """
                WITH LatestActivity AS (
                    SELECT
                        contact_id,
                        message_text,
                        sender_id,
                        activity_time,
                        activity_type,
                        emoji,
                        original_sender_id,
                        ROW_NUMBER() OVER (PARTITION BY contact_id ORDER BY sort_time DESC) as rn,
                        message_type,
                        file_name
                    FROM (
                        SELECT
                            CASE
                                WHEN m.sender_id = %s THEN m.receiver_id
                                ELSE m.sender_id
                            END as contact_id,
                            m.message_text,
                            m.sender_id,
                            m.timestamp as activity_time,
                            'message' as activity_type,
                            NULL as emoji,
                            NULL as original_sender_id,
                            m.timestamp as sort_time,
                            m.message_type,
                            m.file_name
                        FROM messages m
                        WHERE m.sender_id = %s OR m.receiver_id = %s

                        UNION ALL

                        SELECT
                            r.user_id as contact_id,
                            NULL as message_text,
                            r.user_id as sender_id,
                            r.created_at as activity_time,
                            'reaction' as activity_type,
                            r.emoji,
                            m.sender_id as original_sender_id,
                            r.created_at as sort_time,
                            m.message_type,
                            m.file_name
                        FROM reactions r
                        JOIN messages m ON r.message_id = m.id
                        WHERE (m.sender_id = %s OR m.receiver_id = %s)
                            AND r.user_type = 'genius'
                    ) all_activities
                )
                SELECT
                    g.id,
                    g.first_name,
                    g.last_name,
                    g.profile_photo,
                    'genius' as user_type,
                    CASE
                        WHEN la.activity_type = 'reaction' AND la.original_sender_id = %s
                            THEN CONCAT(g.first_name, ' reacted ', la.emoji, ' to your message')
                        ELSE la.message_text
                    END as last_message,
                    COALESCE(la.sender_id, 0) as last_message_sender_id,
                    COALESCE(la.activity_time, NOW()) as last_activity_time,
                    la.message_type as last_message_type,
                    la.file_name as last_file_name
                FROM approve_genius g
                LEFT JOIN LatestActivity la ON la.contact_id = g.id AND la.rn = 1
                WHERE EXISTS (
                    SELECT 1 FROM messages
                    WHERE (sender_id = %s AND receiver_id = g.id)
                    OR (sender_id = g.id AND receiver_id = %s)
                )
                ORDER BY last_activity_time DESC
            """

        cursor.execute(query, (
            current_user_id,  # For contact_id calculation in messages
            current_user_id,  # For messages WHERE clause
            current_user_id,  # For messages WHERE clause
            current_user_id,  # For reactions WHERE clause
            current_user_id,  # For reactions WHERE clause
            current_user_id,  # For reaction message check
            current_user_id,  # For EXISTS clause
            current_user_id   # For EXISTS clause
        ))

        contacts = []
        for contact in cursor.fetchall():
            contact_data = {
                'id': contact['id'],
                'first_name': contact['first_name'],
                'last_name': contact['last_name'],
                'name': f"{contact['first_name']} {contact['last_name']}",
                'user_type': contact['user_type'],
                'last_message': contact['last_message'],
                'last_message_sender_id': contact['last_message_sender_id'],
                'last_activity_time': contact['last_activity_time'],
                'last_message_type': contact.get('last_message_type', 'text'),
                'last_file_name': contact.get('last_file_name', ''),
                'profile_photo_url': f"/get_profile_photo/{contact['id']}"
            }
            contacts.append(contact_data)

        return render_template('message.html',
                             contacts=contacts,
                             current_user_id=current_user_id)

    except Exception as e:
        print(f"Error loading contacts: {str(e)}")
        return render_template('message.html', contacts=[])

    finally:
        if connection:
            conn_manager.release_connection()

def verify_message_ownership(message_id, user_id):
    """Separate function to verify message ownership"""
    connection = None
    cursor = None
    try:
        connection = get_db_connection()
        cursor = connection.cursor()

        # Simple query that should work with any database
        cursor.execute("SELECT 1 FROM messages WHERE id = %s AND sender_id = %s",
                      (message_id, user_id))
        result = cursor.fetchone()

        # If any result is returned, user owns the message
        return result is not None

    except Exception as e:
        print(f"Error in verify_message_ownership: {str(e)}")
        raise
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()



@socketio.on('new_application')
def handle_application(data):
    print("Received application data:", data)  # Debug print
    print("Current user:", session.get('user_id'))  # Debug print

    # Check if user is authenticated and is a genius
    if 'user_id' not in session or session.get('user_type') != 'genius':
        print("Unauthorized application attempt")
        emit('error', {'message': 'Unauthorized'})
        return

    # Validate required data
    if 'client_id' not in data or 'genius_name' not in data:
        print("Missing required data for application")
        emit('error', {'message': 'Missing required data'})
        return

    try:
        connection = get_db_connection()
        cursor = connection.cursor()

        # Check if application already exists
        cursor.execute("""
            SELECT id FROM applications
            WHERE genius_id = %s AND client_id = %s AND status = 'pending'
        """, (session['user_id'], data['client_id']))

        existing = cursor.fetchone()
        if existing:
            print(f"Application already exists with ID: {existing[0]}")
            emit('error', {'message': 'Application already submitted'})
            return

        # Debug print before insert
        print(f"Attempting to insert: genius_id={session['user_id']}, client_id={data['client_id']}")

        cursor.execute("""
            INSERT INTO applications
            (genius_id, client_id, status, created_at, updated_at)
            VALUES
            (%s, %s, 'pending', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """, (session['user_id'], data['client_id']))

        application_id = cursor.lastrowid
        print(f"Insert successful, new ID: {application_id}")  # Debug print

        connection.commit()
        print("Commit successful")  # Debug print

        # Send notification with application ID
        emit('new_application', {
            'application_id': application_id,
            'genius_name': data['genius_name'],
            'position': data.get('position', ''),
            'client_id': data['client_id']
        }, room=f"client_{data['client_id']}")

        # Also send confirmation to the genius
        emit('application_submitted', {
            'application_id': application_id,
            'client_id': data['client_id']
        })

    except Exception as e:
        print(f"Error handling application: {str(e)}")  # This will show the actual error
        if 'connection' in locals() and connection:
            connection.rollback()  # Add rollback on error
        emit('error', {'message': f'Error submitting application: {str(e)}'})
    finally:
        if 'connection' in locals() and connection:
            connection.close()








# Initialize database tables
ensure_news_events_table()
ensure_applications_table()
ensure_portfolio_table()

if __name__ == '__main__':
    app.run(debug=True)
